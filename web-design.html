<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Design - Portfolio</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .project-hero {
            padding: 8rem 4rem 4rem;
            text-align: center;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .project-title {
            font-size: 3.5rem;
            margin-bottom: 1rem;
        }
        
        .project-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .project-content {
            padding: 4rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .project-section {
            margin-bottom: 4rem;
        }
        
        .project-section h2 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
            color: #333;
        }
        
        .project-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .project-image {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .project-image img {
            width: 100%;
            height: 250px;
            object-fit: cover;
        }
        
        .back-button {
            display: inline-block;
            padding: 1rem 2rem;
            background: #f5576c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-bottom: 2rem;
            transition: background 0.3s ease;
        }
        
        .back-button:hover {
            background: #e74c3c;
        }
        
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .tech-tag {
            background: #f8f9fa;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="cursor"></div>
    
    <nav class="nav">
        <div class="logo">Portfolio</div>
        <div class="nav-links">
            <a href="index.html" data-text="Home">Home</a>
            <a href="index.html#work" data-text="Work">Work</a>
            <a href="index.html#about" data-text="About">About</a>
            <a href="index.html#contact" data-text="Contact">Contact</a>
        </div>
    </nav>

    <a href="index.html" class="back-button">← Back to Portfolio</a>

    <section class="project-hero">
        <div class="project-hero-content">
            <div class="project-category">Web Design & Development</div>
            <h1 class="project-title" data-sparkles data-text="Web Design Excellence" data-sparkles-count="10" data-color-first="#f093fb" data-color-second="#f5576c">Web Design Excellence</h1>
            <p class="project-subtitle">Modern, responsive websites that engage users and convert visitors into customers</p>
        </div>
    </section>

    <div class="project-content">
        <a href="index.html#work" class="back-button">← Back to Work</a>
        
        <div class="project-section">
            <h2>Project Overview</h2>
            <p>This web design project involved creating a complete digital experience for a creative agency. The goal was to showcase their portfolio while maintaining excellent user experience across all devices and screen sizes.</p>
        </div>

        <div class="project-section">
            <h2>Technology Stack</h2>
            <div class="tech-stack">
                <span class="tech-tag">HTML5</span>
                <span class="tech-tag">CSS3</span>
                <span class="tech-tag">JavaScript</span>
                <span class="tech-tag">React</span>
                <span class="tech-tag">SCSS</span>
                <span class="tech-tag">Responsive Design</span>
            </div>
        </div>

        <div class="project-section">
            <h2>Design Showcase</h2>
            <div class="project-gallery">
                <div class="project-image">
                    <img src="https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=800" alt="Homepage Design">
                </div>
                <div class="project-image">
                    <img src="https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800" alt="Mobile Responsive">
                </div>
                <div class="project-image">
                    <img src="https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800" alt="User Interface">
                </div>
                <div class="project-image">
                    <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800" alt="Dashboard Design">
                </div>
            </div>
        </div>

        <div class="project-section">
            <h2>Key Features</h2>
            <ul>
                <li>Fully responsive design optimized for all devices</li>
                <li>Interactive animations and micro-interactions</li>
                <li>Fast loading times with optimized performance</li>
                <li>SEO-friendly structure and clean code</li>
                <li>Accessibility compliance (WCAG 2.1)</li>
            </ul>
        </div>

        <div class="project-section">
            <h2>Results</h2>
            <p>The new website achieved a 60% increase in user engagement and a 35% improvement in conversion rates. The responsive design ensured consistent experience across all devices, leading to a 45% increase in mobile traffic.</p>
        </div>
    </div>

    <script src="sparkles-text.js"></script>
    <script src="parallax.js"></script>
    <script src="script.js"></script>
</body>
</html>

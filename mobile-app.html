<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile App Design - Portfolio</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .project-hero {
            padding: 8rem 4rem 4rem;
            text-align: center;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .project-title {
            font-size: 3.5rem;
            margin-bottom: 1rem;
        }
        
        .project-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .project-content {
            padding: 4rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .project-section {
            margin-bottom: 4rem;
        }
        
        .project-section h2 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
            color: #333;
        }
        
        .project-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .project-image {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .project-image img {
            width: 100%;
            height: 250px;
            object-fit: cover;
        }
        
        .back-button {
            display: inline-block;
            padding: 1rem 2rem;
            background: #4facfe;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-bottom: 2rem;
            transition: background 0.3s ease;
        }
        
        .back-button:hover {
            background: #3498db;
        }
        
        .app-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="cursor"></div>
    
    <nav class="nav">
        <div class="logo">Portfolio</div>
        <div class="nav-links">
            <a href="index.html" data-text="Home">Home</a>
            <a href="index.html#work" data-text="Work">Work</a>
            <a href="index.html#about" data-text="About">About</a>
            <a href="index.html#contact" data-text="Contact">Contact</a>
        </div>
    </nav>

    <section class="project-hero">
        <h1 class="project-title">Mobile App Design</h1>
        <p class="project-subtitle">Intuitive iOS & Android applications that users love</p>
    </section>

    <div class="project-content">
        <a href="index.html#work" class="back-button">← Back to Work</a>
        
        <div class="project-section">
            <h2>Project Overview</h2>
            <p>This mobile app design project focused on creating a fitness tracking application that motivates users to maintain healthy habits. The app needed to be both functional and visually appealing, with seamless user experience across iOS and Android platforms.</p>
        </div>

        <div class="project-section">
            <h2>Key Features</h2>
            <div class="app-features">
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3>Intuitive Interface</h3>
                    <p>Clean, modern design that's easy to navigate</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>Progress Tracking</h3>
                    <p>Visual charts and statistics to monitor goals</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔔</div>
                    <h3>Smart Notifications</h3>
                    <p>Personalized reminders and achievements</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h3>Custom Themes</h3>
                    <p>Multiple color schemes and personalization</p>
                </div>
            </div>
        </div>

        <div class="project-section">
            <h2>Design Showcase</h2>
            <div class="project-gallery">
                <div class="project-image">
                    <img src="https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=800" alt="App Interface">
                </div>
                <div class="project-image">
                    <img src="https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800" alt="Mobile Screens">
                </div>
                <div class="project-image">
                    <img src="https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=800" alt="User Experience">
                </div>
                <div class="project-image">
                    <img src="https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=800" alt="App Prototype">
                </div>
            </div>
        </div>

        <div class="project-section">
            <h2>Design Process</h2>
            <p>The design process involved extensive user research, wireframing, and prototyping. We conducted usability testing with target users to ensure the app met their needs and expectations. The final design incorporates feedback from multiple testing rounds.</p>
        </div>

        <div class="project-section">
            <h2>Results</h2>
            <p>The app launched successfully on both iOS and Android platforms, achieving over 50,000 downloads in the first month. User retention rate reached 75% after 30 days, significantly higher than the industry average of 25%.</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>

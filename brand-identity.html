<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brand Identity - Portfolio</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .project-hero {
            padding: 8rem 4rem 4rem;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
            overflow: hidden;
            min-height: 80vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .project-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            animation: heroFloat 12s ease-in-out infinite;
        }

        @keyframes heroFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(2deg); }
        }

        .project-hero-content {
            position: relative;
            z-index: 2;
        }

        .project-category {
            color: #667eea;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            display: inline-block;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.9rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            margin-bottom: 2rem;
        }

        .project-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 900;
            margin: 1rem 0;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .project-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .project-content {
            padding: 4rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
        }

        .project-section {
            margin-bottom: 4rem;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .project-section h2 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
            color: #fff;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .project-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .project-image {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(0,0,0,0.2);
            transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
        }

        .project-image:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .project-image img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            filter: brightness(0.9) contrast(1.1);
            transition: filter 0.3s ease;
        }

        .project-image:hover img {
            filter: brightness(1) contrast(1.2);
        }

        .project-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .stat-item {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 900;
            color: #667eea;
            display: block;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.8;
            margin-top: 0.5rem;
        }

        .back-button {
            position: fixed;
            top: 2rem;
            left: 2rem;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 1rem 1.5rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-5px);
        }
    </style>
</head>
<body>
    <div class="cursor"></div>
    
    <nav class="nav">
        <div class="logo">Portfolio</div>
        <div class="nav-links">
            <a href="index.html" data-text="Home">Home</a>
            <a href="index.html#work" data-text="Work">Work</a>
            <a href="index.html#about" data-text="About">About</a>
            <a href="index.html#contact" data-text="Contact">Contact</a>
        </div>
    </nav>

    <a href="index.html" class="back-button">← Back to Portfolio</a>

    <section class="project-hero">
        <div class="project-hero-content">
            <div class="project-category">Branding & Identity</div>
            <h1 class="project-title" data-sparkles data-text="Brand Identity Design" data-sparkles-count="12" data-color-first="#667eea" data-color-second="#764ba2">Brand Identity Design</h1>
            <p class="project-subtitle">Creating memorable visual identities that resonate with audiences and build lasting brand connections</p>
        </div>
    </section>

    <div class="project-content">
        <div class="project-section">
            <h2>Project Overview</h2>
            <p>This brand identity project focused on creating a cohesive visual system for a modern tech startup. The challenge was to develop a brand that felt both innovative and trustworthy, appealing to both B2B clients and end consumers.</p>

            <div class="project-stats">
                <div class="stat-item">
                    <span class="stat-number">40%</span>
                    <span class="stat-label">Brand Recognition Increase</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">25%</span>
                    <span class="stat-label">Client Inquiries Growth</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">6</span>
                    <span class="stat-label">Weeks to Complete</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">100%</span>
                    <span class="stat-label">Client Satisfaction</span>
                </div>
            </div>
        </div>

        <div class="project-section">
            <h2>Design Process</h2>
            <p>The design process began with extensive research into the target market and competitor analysis. We developed multiple logo concepts, exploring different typographic treatments and symbolic representations before settling on the final design.</p>
        </div>

        <div class="project-section">
            <h2>Visual Gallery</h2>
            <div class="project-gallery">
                <div class="project-image">
                    <img src="https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=800" alt="Logo Design">
                </div>
                <div class="project-image">
                    <img src="https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=800" alt="Business Cards">
                </div>
                <div class="project-image">
                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800" alt="Brand Guidelines">
                </div>
                <div class="project-image">
                    <img src="https://images.unsplash.com/photo-1542744094-3a31f272c490?w=800" alt="Stationery Design">
                </div>
            </div>
        </div>

        <div class="project-section">
            <h2>Results</h2>
            <p>The new brand identity successfully positioned the company as a leader in their industry. The cohesive visual system improved brand recognition by 40% and contributed to a 25% increase in client inquiries within the first quarter of implementation.</p>
        </div>
    </div>

    <script src="sparkles-text.js"></script>
    <script src="parallax.js"></script>
    <script src="script.js"></script>
</body>
</html>

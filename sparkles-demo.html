<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SparklesText Demo - Portfolio</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .demo-container {
            padding: 4rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }
        
        .demo-section {
            margin: 4rem 0;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .demo-title {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #333;
        }
        
        .demo-text {
            margin: 2rem 0;
        }
        
        .sparkles-large {
            font-size: 4rem;
            font-weight: bold;
            margin: 2rem 0;
        }
        
        .sparkles-medium {
            font-size: 2.5rem;
            font-weight: 600;
            margin: 1.5rem 0;
        }
        
        .sparkles-small {
            font-size: 1.5rem;
            font-weight: 500;
            margin: 1rem 0;
        }
        
        .color-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .color-card {
            padding: 2rem;
            background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .back-button {
            display: inline-block;
            padding: 1rem 2rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            margin: 2rem 0;
            transition: transform 0.3s ease;
        }
        
        .back-button:hover {
            transform: translateY(-2px);
        }
        
        .usage-code {
            background: #1a1a1a;
            color: #f8f8f2;
            padding: 1.5rem;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            text-align: left;
            margin: 1rem 0;
            overflow-x: auto;
        }
        
        .usage-code pre {
            margin: 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="cursor"></div>
    
    <nav class="nav">
        <div class="logo">Portfolio</div>
        <div class="nav-links">
            <a href="index.html" data-text="Home">Home</a>
            <a href="index.html#work" data-text="Work">Work</a>
            <a href="index.html#about" data-text="About">About</a>
            <a href="index.html#contact" data-text="Contact">Contact</a>
        </div>
    </nav>

    <div class="demo-container">
        <a href="index.html" class="back-button">← Back to Home</a>
        
        <h1 data-sparkles data-text="SparklesText Demo" data-sparkles-count="15" data-color-first="#9E7AFF" data-color-second="#FE8BBB">SparklesText Demo</h1>
        
        <div class="demo-section">
            <h2 class="demo-title">Different Sizes</h2>
            
            <div class="sparkles-large" data-sparkles data-text="Large Text" data-sparkles-count="12" data-color-first="#667eea" data-color-second="#764ba2">
                Large Text
            </div>
            
            <div class="sparkles-medium" data-sparkles data-text="Medium Text" data-sparkles-count="8" data-color-first="#f093fb" data-color-second="#f5576c">
                Medium Text
            </div>
            
            <div class="sparkles-small" data-sparkles data-text="Small Text" data-sparkles-count="5" data-color-first="#4facfe" data-color-second="#00f2fe">
                Small Text
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">Different Color Schemes</h2>
            
            <div class="color-demo">
                <div class="color-card">
                    <h3>Purple & Pink</h3>
                    <div data-sparkles data-text="Magic UI" data-sparkles-count="10" data-color-first="#9E7AFF" data-color-second="#FE8BBB" style="font-size: 2rem; font-weight: bold;">
                        Magic UI
                    </div>
                </div>
                
                <div class="color-card">
                    <h3>Blue & Cyan</h3>
                    <div data-sparkles data-text="Ocean Wave" data-sparkles-count="10" data-color-first="#4facfe" data-color-second="#00f2fe" style="font-size: 2rem; font-weight: bold;">
                        Ocean Wave
                    </div>
                </div>
                
                <div class="color-card">
                    <h3>Orange & Red</h3>
                    <div data-sparkles data-text="Fire Spark" data-sparkles-count="10" data-color-first="#fa709a" data-color-second="#fee140" style="font-size: 2rem; font-weight: bold;">
                        Fire Spark
                    </div>
                </div>
                
                <div class="color-card">
                    <h3>Green & Teal</h3>
                    <div data-sparkles data-text="Nature Fresh" data-sparkles-count="10" data-color-first="#a8edea" data-color-second="#fed6e3" style="font-size: 2rem; font-weight: bold;">
                        Nature Fresh
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">Usage Examples</h2>
            
            <h3>HTML Attributes (Automatic)</h3>
            <div class="usage-code">
                <pre>&lt;div data-sparkles 
     data-text="Your Text" 
     data-sparkles-count="10" 
     data-color-first="#9E7AFF" 
     data-color-second="#FE8BBB"&gt;
    Your Text
&lt;/div&gt;</pre>
            </div>
            
            <h3>JavaScript (Manual)</h3>
            <div class="usage-code">
                <pre>const element = document.querySelector('.my-text');
const sparkles = new SparklesText(element, {
    text: 'Magic UI',
    sparklesCount: 10,
    colors: {
        first: '#9E7AFF',
        second: '#FE8BBB'
    }
});</pre>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">Interactive Demo</h2>
            <p>Click the button below to see dynamic text change:</p>
            
            <div id="interactive-text" data-sparkles data-text="Click Below!" data-sparkles-count="8" data-color-first="#667eea" data-color-second="#764ba2" style="font-size: 2.5rem; font-weight: bold; margin: 2rem 0;">
                Click Below!
            </div>
            
            <button onclick="changeText()" style="padding: 1rem 2rem; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 25px; cursor: pointer; font-size: 1rem;">
                Change Text
            </button>
        </div>
    </div>

    <script src="sparkles-text.js"></script>
    <script src="script.js"></script>
    <script>
        const texts = ['Amazing!', 'Fantastic!', 'Wonderful!', 'Brilliant!', 'Spectacular!'];
        let currentIndex = 0;
        
        function changeText() {
            const element = document.getElementById('interactive-text');
            const textElement = element.querySelector('strong');
            if (textElement) {
                currentIndex = (currentIndex + 1) % texts.length;
                textElement.textContent = texts[currentIndex];
            }
        }
    </script>
</body>
</html>

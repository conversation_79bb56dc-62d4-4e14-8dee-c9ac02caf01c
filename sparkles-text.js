// Vanilla JavaScript version of SparklesText component
class SparklesText {
    constructor(element, options = {}) {
        this.element = element;
        this.text = options.text || element.textContent;
        this.sparklesCount = options.sparklesCount || 10;
        this.colors = options.colors || { first: '#9E7AFF', second: '#FE8BBB' };
        this.sparkles = [];
        
        this.init();
    }
    
    init() {
        // Clear existing content and set up container
        this.element.innerHTML = '';
        this.element.style.position = 'relative';
        this.element.style.display = 'inline-block';
        this.element.style.fontSize = this.element.style.fontSize || '3rem';
        this.element.style.fontWeight = 'bold';
        
        // Create text element
        const textElement = document.createElement('strong');
        textElement.textContent = this.text;
        textElement.style.position = 'relative';
        textElement.style.zIndex = '10';
        this.element.appendChild(textElement);
        
        // Initialize sparkles
        this.generateSparkles();
        this.startAnimation();
    }
    
    generateSparkles() {
        for (let i = 0; i < this.sparklesCount; i++) {
            this.createSparkle();
        }
    }
    
    createSparkle() {
        const sparkle = {
            id: Math.random().toString(36).substr(2, 9),
            x: Math.random() * 100,
            y: Math.random() * 100,
            color: Math.random() > 0.5 ? this.colors.first : this.colors.second,
            delay: Math.random() * 2,
            scale: Math.random() * 1 + 0.3,
            lifespan: Math.random() * 10 + 5,
            element: null
        };
        
        // Create SVG sparkle element
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', '21');
        svg.setAttribute('height', '21');
        svg.setAttribute('viewBox', '0 0 21 21');
        svg.style.position = 'absolute';
        svg.style.left = sparkle.x + '%';
        svg.style.top = sparkle.y + '%';
        svg.style.pointerEvents = 'none';
        svg.style.zIndex = '20';
        svg.style.opacity = '0';
        
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', 'M9.82531 0.843845C10.0553 0.215178 10.9446 0.215178 11.1746 0.843845L11.8618 2.72026C12.4006 4.19229 12.3916 6.39157 13.5 7.5C14.6084 8.60843 16.8077 8.59935 18.2797 9.13822L20.1561 9.82534C20.7858 10.0553 20.7858 10.9447 20.1561 11.1747L18.2797 11.8618C16.8077 12.4007 14.6084 12.3916 13.5 13.5C12.3916 14.6084 12.4006 16.8077 11.8618 18.2798L11.1746 20.1562C10.9446 20.7858 10.0553 20.7858 9.82531 20.1562L9.13819 18.2798C8.59932 16.8077 8.60843 14.6084 7.5 13.5C6.39157 12.3916 4.19225 12.4007 2.72023 11.8618L0.843814 11.1747C0.215148 10.9447 0.215148 10.0553 0.843814 9.82534L2.72023 9.13822C4.19225 8.59935 6.39157 8.60843 7.5 7.5C8.60843 6.39157 8.59932 4.19229 9.13819 2.72026L9.82531 0.843845Z');
        path.setAttribute('fill', sparkle.color);
        
        svg.appendChild(path);
        sparkle.element = svg;
        
        this.element.appendChild(svg);
        this.sparkles.push(sparkle);
        
        return sparkle;
    }
    
    animateSparkle(sparkle) {
        if (!sparkle.element) return;
        
        const duration = 800;
        const startTime = Date.now() + (sparkle.delay * 1000);
        
        const animate = () => {
            const now = Date.now();
            const elapsed = now - startTime;
            
            if (elapsed < 0) {
                requestAnimationFrame(animate);
                return;
            }
            
            const progress = (elapsed % duration) / duration;
            
            // Opacity animation (0 -> 1 -> 0)
            let opacity;
            if (progress < 0.5) {
                opacity = progress * 2;
            } else {
                opacity = (1 - progress) * 2;
            }
            
            // Scale animation
            const scale = Math.sin(progress * Math.PI) * sparkle.scale;
            
            // Rotation animation
            const rotation = 75 + (progress * 75);
            
            sparkle.element.style.opacity = opacity;
            sparkle.element.style.transform = `scale(${scale}) rotate(${rotation}deg)`;
            
            // Continue animation
            if (sparkle.lifespan > 0) {
                sparkle.lifespan -= 0.1;
                requestAnimationFrame(animate);
            } else {
                // Reset sparkle
                sparkle.x = Math.random() * 100;
                sparkle.y = Math.random() * 100;
                sparkle.color = Math.random() > 0.5 ? this.colors.first : this.colors.second;
                sparkle.lifespan = Math.random() * 10 + 5;
                sparkle.element.style.left = sparkle.x + '%';
                sparkle.element.style.top = sparkle.y + '%';
                sparkle.element.querySelector('path').setAttribute('fill', sparkle.color);
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    startAnimation() {
        this.sparkles.forEach(sparkle => {
            this.animateSparkle(sparkle);
        });
    }
    
    updateText(newText) {
        this.text = newText;
        const textElement = this.element.querySelector('strong');
        if (textElement) {
            textElement.textContent = newText;
        }
    }
    
    updateColors(newColors) {
        this.colors = { ...this.colors, ...newColors };
    }
    
    destroy() {
        this.element.innerHTML = this.text;
        this.sparkles = [];
    }
}

// Auto-initialize sparkles text elements
document.addEventListener('DOMContentLoaded', function() {
    const sparklesElements = document.querySelectorAll('[data-sparkles]');
    sparklesElements.forEach(element => {
        const options = {
            text: element.getAttribute('data-text') || element.textContent,
            sparklesCount: parseInt(element.getAttribute('data-sparkles-count')) || 10,
            colors: {
                first: element.getAttribute('data-color-first') || '#9E7AFF',
                second: element.getAttribute('data-color-second') || '#FE8BBB'
            }
        };
        new SparklesText(element, options);
    });
});

// Export for manual initialization
window.SparklesText = SparklesText;

// Parallax Scroll Effects
class ParallaxController {
    constructor() {
        this.elements = [];
        this.isScrolling = false;
        this.init();
    }
    
    init() {
        this.setupElements();
        this.bindEvents();
        this.animate();
    }
    
    setupElements() {
        // Hero parallax elements
        this.elements.push({
            element: document.querySelector('.hero::before'),
            speed: 0.5,
            type: 'background'
        });
        
        this.elements.push({
            element: document.querySelector('.hero-content'),
            speed: 0.3,
            type: 'transform'
        });
        
        // Portfolio parallax elements
        this.elements.push({
            element: document.querySelector('.portfolio::before'),
            speed: 0.4,
            type: 'background'
        });
        
        // Portfolio items
        const portfolioItems = document.querySelectorAll('.portfolio-item');
        portfolioItems.forEach((item, index) => {
            this.elements.push({
                element: item,
                speed: 0.1 + (index * 0.05),
                type: 'transform',
                offset: index * 100
            });
        });
    }
    
    bindEvents() {
        let ticking = false;
        
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.updateParallax();
                    ticking = false;
                });
                ticking = true;
            }
        });
        
        // Smooth scroll for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }
    
    updateParallax() {
        const scrollTop = window.pageYOffset;
        const windowHeight = window.innerHeight;
        
        this.elements.forEach(item => {
            if (!item.element) return;
            
            const elementTop = item.element.offsetTop || 0;
            const elementHeight = item.element.offsetHeight || 0;
            const offset = item.offset || 0;
            
            // Check if element is in viewport
            if (elementTop < scrollTop + windowHeight && elementTop + elementHeight > scrollTop) {
                const yPos = -(scrollTop * item.speed) + offset;
                
                if (item.type === 'transform') {
                    item.element.style.transform = `translate3d(0, ${yPos}px, 0)`;
                } else if (item.type === 'background') {
                    item.element.style.transform = `translate3d(0, ${yPos}px, 0)`;
                }
            }
        });
    }
    
    animate() {
        this.updateParallax();
        requestAnimationFrame(() => this.animate());
    }
}

// Scroll-triggered animations
class ScrollAnimations {
    constructor() {
        this.elements = [];
        this.init();
    }
    
    init() {
        this.setupObserver();
        this.findElements();
    }
    
    setupObserver() {
        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    this.observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });
    }
    
    findElements() {
        // Portfolio items
        const portfolioItems = document.querySelectorAll('.portfolio-item');
        portfolioItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(50px)';
            item.style.transition = `all 0.8s cubic-bezier(0.23, 1, 0.320, 1) ${index * 0.1}s`;
            this.observer.observe(item);
        });
        
        // Section titles
        const sectionTitles = document.querySelectorAll('.section-title');
        sectionTitles.forEach(title => {
            title.style.opacity = '0';
            title.style.transform = 'translateY(30px)';
            title.style.transition = 'all 0.8s cubic-bezier(0.23, 1, 0.320, 1)';
            this.observer.observe(title);
        });
    }
}

// Enhanced cursor effects
class EnhancedCursor {
    constructor() {
        this.cursor = document.querySelector('.cursor');
        this.isHovering = false;
        this.init();
    }
    
    init() {
        if (!this.cursor) return;
        
        document.addEventListener('mousemove', (e) => {
            this.cursor.style.left = e.clientX + 'px';
            this.cursor.style.top = e.clientY + 'px';
        });
        
        // Portfolio item hover effects
        document.querySelectorAll('.portfolio-item').forEach(item => {
            item.addEventListener('mouseenter', () => {
                this.cursor.classList.add('cursor-hover');
                this.cursor.innerHTML = '<span>View Project</span>';
            });
            
            item.addEventListener('mouseleave', () => {
                this.cursor.classList.remove('cursor-hover');
                this.cursor.innerHTML = '';
            });
        });
        
        // Navigation hover effects
        document.querySelectorAll('.nav-links a').forEach(link => {
            link.addEventListener('mouseenter', () => {
                this.cursor.classList.add('cursor-link');
            });
            
            link.addEventListener('mouseleave', () => {
                this.cursor.classList.remove('cursor-link');
            });
        });
    }
}

// Smooth page transitions
class PageTransitions {
    constructor() {
        this.init();
    }
    
    init() {
        // Add loading animation
        window.addEventListener('load', () => {
            document.body.classList.add('loaded');
        });
        
        // Add page transition for portfolio links
        document.querySelectorAll('.portfolio-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const href = link.getAttribute('href');
                
                // Add transition effect
                document.body.classList.add('page-transition');
                
                setTimeout(() => {
                    window.location.href = href;
                }, 500);
            });
        });
    }
}

// Initialize all effects when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ParallaxController();
    new ScrollAnimations();
    new EnhancedCursor();
    new PageTransitions();
});

// Add CSS for animations
const animationStyles = `
    .animate-in {
        opacity: 1 !important;
        transform: translateY(0) !important;
    }
    
    .cursor-hover {
        transform: scale(3) !important;
        background: rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(10px) !important;
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
    }
    
    .cursor-hover span {
        font-size: 12px;
        font-weight: 500;
        color: #fff;
        text-align: center;
        line-height: 1;
    }
    
    .cursor-link {
        transform: scale(1.5) !important;
        background: rgba(102, 126, 234, 0.3) !important;
    }
    
    body.loaded {
        opacity: 1;
    }
    
    body.page-transition {
        opacity: 0;
        transition: opacity 0.5s ease;
    }
    
    body {
        opacity: 0;
        transition: opacity 0.3s ease;
    }
`;

// Inject styles
const styleSheet = document.createElement('style');
styleSheet.textContent = animationStyles;
document.head.appendChild(styleSheet);

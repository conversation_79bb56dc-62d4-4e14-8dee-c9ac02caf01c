* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: #0a0a0a;
    color: #fff;
    overflow-x: hidden;
    cursor: none;
}

/* Custom Cursor */
.cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    background: #fff;
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
    transition: transform 0.1s ease;
}

/* Navigation */
.nav {
    position: fixed;
    top: 0;
    width: 100%;
    padding: 2rem 4rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-links a {
    color: #fff;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    transition: color 0.3s ease;
}

.nav-links a::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.nav-links a:hover::before {
    transform: translateY(0);
}

.nav-links a:hover {
    transform: translateY(100%);
}

/* Hero Section */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(45deg, #0a0a0a, #1a1a1a);
}

.hero-title {
    font-size: clamp(4rem, 8vw, 8rem);
    font-weight: 900;
    line-height: 0.9;
    text-align: center;
}

.hero-title .line {
    display: block;
    overflow: hidden;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-top: 2rem;
    opacity: 0.7;
    text-align: center;
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
}

.scroll-line {
    width: 2px;
    height: 60px;
    background: #fff;
    position: relative;
    opacity: 0.5;
}

.scroll-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 30px;
    background: #fff;
    animation: scroll 2s infinite;
}

@keyframes scroll {
    0% { transform: translateY(0); opacity: 1; }
    100% { transform: translateY(30px); opacity: 0; }
}

/* Portfolio Section */
.portfolio {
    padding: 8rem 4rem;
    min-height: 100vh;
}

.section-title {
    font-size: 3rem;
    margin-bottom: 4rem;
    text-align: center;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
}

.portfolio-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    cursor: none;
    transition: transform 0.5s ease;
}

.portfolio-item:hover {
    transform: scale(1.05);
}

.portfolio-image {
    width: 100%;
    height: 300px;
    overflow: hidden;
}

.portfolio-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.1);
}

.portfolio-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 2rem;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.portfolio-item:hover .portfolio-info {
    transform: translateY(0);
}

/* Responsive */
@media (max-width: 768px) {
    .nav {
        padding: 1rem 2rem;
    }
    
    .portfolio {
        padding: 4rem 2rem;
    }
    
    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    body {
        cursor: auto;
    }
    
    .cursor {
        display: none;
    }
}
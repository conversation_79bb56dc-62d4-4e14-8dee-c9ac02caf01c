* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: #0a0a0a;
    color: #fff;
    overflow-x: hidden;
    cursor: none;
}

/* Custom Cursor */
.cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    background: #fff;
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
    transition: transform 0.1s ease;
}

/* Navigation */
.nav {
    position: fixed;
    top: 0;
    width: 100%;
    padding: 2rem 4rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-links a {
    color: #fff;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    transition: color 0.3s ease;
}

.nav-links a::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.nav-links a:hover::before {
    transform: translateY(0);
}

.nav-links a:hover {
    transform: translateY(100%);
}

/* Hero Section */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.08)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.06)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    animation: float 20s ease-in-out infinite;
    will-change: transform;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
    transform: translateZ(0);
    will-change: transform;
}

.hero-title {
    font-size: clamp(4rem, 8vw, 8rem);
    font-weight: 900;
    line-height: 0.9;
    text-align: center;
}

.hero-title .line {
    display: block;
    overflow: hidden;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-top: 2rem;
    opacity: 0.7;
    text-align: center;
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
}

.scroll-line {
    width: 2px;
    height: 60px;
    background: #fff;
    position: relative;
    opacity: 0.5;
}

.scroll-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 30px;
    background: #fff;
    animation: scroll 2s infinite;
}

@keyframes scroll {
    0% { transform: translateY(0); opacity: 1; }
    100% { transform: translateY(30px); opacity: 0; }
}

/* Portfolio Section */
.portfolio {
    padding: 8rem 2rem;
    min-height: 100vh;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    position: relative;
    overflow: hidden;
}

.portfolio::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    animation: parallaxFloat 15s ease-in-out infinite;
    will-change: transform;
}

@keyframes parallaxFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-20px) scale(1.05); }
}

.section-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 4rem;
    text-align: center;
    position: relative;
    z-index: 2;
    color: #fff;
    text-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.portfolio-item {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    will-change: transform;
}

.portfolio-item:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
}

.portfolio-image {
    width: 100%;
    height: 280px;
    overflow: hidden;
    border-radius: 15px;
    position: relative;
    margin-bottom: 1.5rem;
}

.portfolio-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
    filter: brightness(0.9) contrast(1.1);
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.08);
    filter: brightness(1) contrast(1.2);
}

.portfolio-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.portfolio-item:hover .portfolio-image::after {
    opacity: 1;
}

.portfolio-info {
    padding: 1.5rem;
    color: #fff;
    position: relative;
}

.portfolio-info h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #fff;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.portfolio-info p {
    font-size: 0.95rem;
    opacity: 0.9;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.portfolio-category {
    display: inline-block;
    padding: 0.4rem 1rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.portfolio-item:hover .portfolio-category {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.portfolio-link {
    display: block;
    text-decoration: none;
    color: inherit;
    width: 100%;
    height: 100%;
}

/* Enhanced Cursor Effects */
.cursor {
    width: 20px;
    height: 20px;
    border: 2px solid #fff;
    border-radius: 50%;
    position: fixed;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.320, 1);
    backdrop-filter: blur(5px);
    background: rgba(255, 255, 255, 0.1);
}

.cursor.cursor-hover {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.cursor.cursor-link {
    width: 30px;
    height: 30px;
    background: rgba(102, 126, 234, 0.3);
    border-color: rgba(102, 126, 234, 0.5);
}

/* Smooth page transitions */
body {
    opacity: 0;
    transition: opacity 0.5s ease;
}

body.loaded {
    opacity: 1;
}

body.page-transition {
    opacity: 0;
}

/* Responsive */
@media (max-width: 768px) {
    .nav {
        padding: 1rem 2rem;
    }
    
    .portfolio {
        padding: 4rem 2rem;
    }
    
    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    body {
        cursor: auto;
    }
    
    .cursor {
        display: none;
    }
}
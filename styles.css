* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: #0a0a0a;
    color: #fff;
    overflow-x: hidden;
    cursor: none;
}

/* Custom Cursor */
.cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    background: #fff;
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
    transition: transform 0.1s ease;
}

/* Navigation */
.nav {
    position: fixed;
    top: 0;
    width: 100%;
    padding: 2rem 4rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-links a {
    color: #fff;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    transition: color 0.3s ease;
}

.nav-links a::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.nav-links a:hover::before {
    transform: translateY(0);
}

.nav-links a:hover {
    transform: translateY(100%);
}

/* Hero Section */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
}

/* Floating particles animation */
.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.4), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.2), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(255,255,255,0.3), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: floatingParticles 20s linear infinite;
    z-index: 1;
}

@keyframes floatingParticles {
    0% {
        transform: translateY(0px) translateX(0px);
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(50px);
        opacity: 0;
    }
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
    animation: heroFloat 15s ease-in-out infinite;
    will-change: transform;
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 0.03) 50%, transparent 70%);
    animation: heroShimmer 10s ease-in-out infinite;
    will-change: transform;
}

@keyframes heroFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 1;
    }
    33% {
        transform: translateY(-15px) rotate(1deg) scale(1.02);
        opacity: 0.9;
    }
    66% {
        transform: translateY(-25px) rotate(-1deg) scale(0.98);
        opacity: 0.95;
    }
}

@keyframes heroShimmer {
    0%, 100% {
        transform: translateX(-100%) rotate(45deg);
        opacity: 0;
    }
    50% {
        transform: translateX(100%) rotate(45deg);
        opacity: 1;
    }
}

.hero-content {
    position: relative;
    z-index: 10;
    transform: translateZ(0);
    will-change: transform;
    text-align: center;
    color: white;
}

.hero-title {
    font-size: clamp(4rem, 8vw, 8rem);
    font-weight: 900;
    line-height: 0.9;
    text-align: center;
}

.hero-title .line {
    display: block;
    overflow: hidden;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-top: 2rem;
    opacity: 0.7;
    text-align: center;
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    opacity: 0;
    animation: fadeInDelay 1s ease forwards 2s;
}

@keyframes fadeInDelay {
    to { opacity: 1; }
}

.scroll-line {
    width: 2px;
    height: 60px;
    background: rgba(255, 255, 255, 0.8);
    position: relative;
    border-radius: 2px;
    backdrop-filter: blur(5px);
}

.scroll-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 30px;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
    border-radius: 2px;
    animation: scroll 2s ease-in-out infinite;
}

@keyframes scroll {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateY(30px);
        opacity: 0;
    }
}

/* Portfolio Section */
.portfolio {
    padding: 8rem 2rem;
    min-height: 100vh;
    background: #0f0f0f;
    position: relative;
    overflow: hidden;
}

.portfolio::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.08) 0%, transparent 50%);
    animation: portfolioFloat 20s ease-in-out infinite;
    will-change: transform;
}

@keyframes portfolioFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-30px) scale(1.02);
        opacity: 1;
    }
}

.section-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 4rem;
    text-align: center;
    position: relative;
    z-index: 2;
    color: #fff;
    text-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.portfolio-item {
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    will-change: transform;
    opacity: 0;
    transform: translateY(50px);
    animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.portfolio-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 16px;
}

.portfolio-item:hover::before {
    opacity: 1;
}

.portfolio-item:hover {
    transform: translateY(-8px);
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.portfolio-image {
    width: 100%;
    height: 280px;
    overflow: hidden;
    border-radius: 15px;
    position: relative;
    margin-bottom: 1.5rem;
}

.portfolio-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
    filter: brightness(0.9) contrast(1.1);
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.08);
    filter: brightness(1) contrast(1.2);
}

.portfolio-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.portfolio-item:hover .portfolio-image::after {
    opacity: 1;
}

.portfolio-info {
    padding: 1.5rem;
    color: #fff;
    position: relative;
}

.portfolio-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #fff;
    letter-spacing: -0.02em;
}

.portfolio-info p {
    font-size: 0.9rem;
    opacity: 0.7;
    line-height: 1.5;
    margin-bottom: 1rem;
    color: #e0e0e0;
}

.portfolio-category {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
    color: rgba(255, 255, 255, 0.8);
}

.portfolio-item:hover .portfolio-category {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.portfolio-link {
    display: block;
    text-decoration: none;
    color: inherit;
    width: 100%;
    height: 100%;
}

/* Enhanced Cursor Effects */
.cursor {
    width: 20px;
    height: 20px;
    border: 2px solid #fff;
    border-radius: 50%;
    position: fixed;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.320, 1);
    backdrop-filter: blur(5px);
    background: rgba(255, 255, 255, 0.1);
}

.cursor.cursor-hover {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.cursor.cursor-link {
    width: 30px;
    height: 30px;
    background: rgba(102, 126, 234, 0.3);
    border-color: rgba(102, 126, 234, 0.5);
}

/* Smooth page transitions */
body {
    opacity: 0;
    transition: opacity 0.5s ease;
}

body.loaded {
    opacity: 1;
}

body.page-transition {
    opacity: 0;
}

/* Scroll Animation Classes */
.animate-in {
    opacity: 1 !important;
    transform: translateY(0) !important;
}

/* Smooth scrolling for the entire page */
html {
    scroll-behavior: smooth;
}

/* Enhanced scroll animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(60px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-60px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(60px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Staggered animation delays */
.portfolio-item:nth-child(1) { animation-delay: 0.1s; }
.portfolio-item:nth-child(2) { animation-delay: 0.2s; }
.portfolio-item:nth-child(3) { animation-delay: 0.3s; }
.portfolio-item:nth-child(4) { animation-delay: 0.4s; }
.portfolio-item:nth-child(5) { animation-delay: 0.5s; }
.portfolio-item:nth-child(6) { animation-delay: 0.6s; }

/* Responsive */
@media (max-width: 768px) {
    .nav {
        padding: 1rem 2rem;
    }
    
    .portfolio {
        padding: 4rem 2rem;
    }
    
    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    body {
        cursor: auto;
    }
    
    .cursor {
        display: none;
    }
}